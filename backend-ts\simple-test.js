console.log('Starting simple test...');

async function test() {
  try {
    console.log('Loading dotenv...');
    require('dotenv').config();
    
    console.log('Environment variables:');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Set' : 'Not set');
    console.log('PORT:', process.env.PORT);
    
    console.log('Loading NestJS...');
    const { NestFactory } = require('@nestjs/core');
    const { FastifyAdapter } = require('@nestjs/platform-fastify');
    
    console.log('Loading AppModule...');
    const { AppModule } = require('./dist/src/app.module');
    
    console.log('Creating NestJS app...');
    const app = await NestFactory.create(
      AppModule,
      new FastifyAdapter({ logger: false })
    );
    
    console.log('App created successfully!');
    
    console.log('Starting server on port 3000...');
    await app.listen(3000, '0.0.0.0');
    console.log('Server started successfully on http://localhost:3000');
    
  } catch (error) {
    console.error('Error in test:', error);
    console.error('Stack trace:', error.stack);
  }
}

test();
