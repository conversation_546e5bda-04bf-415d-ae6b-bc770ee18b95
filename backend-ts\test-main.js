console.log('Starting test...');

try {
  console.log('Loading dotenv...');
  require('dotenv').config();
  console.log('Dotenv loaded');

  console.log('Loading NestJS modules...');
  const { NestFactory } = require('@nestjs/core');
  const { FastifyAdapter } = require('@nestjs/platform-fastify');
  console.log('NestJS modules loaded');

  console.log('Loading AppModule...');
  const { AppModule } = require('./dist/src/app.module');
  console.log('AppModule loaded');

  console.log('All modules loaded successfully');
} catch (error) {
  console.error('Error loading modules:', error);
}
