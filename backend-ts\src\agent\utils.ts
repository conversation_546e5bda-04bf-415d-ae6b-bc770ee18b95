import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { Citation, CitationSegment } from './types';

/**
 * Get the research topic from the messages.
 * Equivalent to the Python get_research_topic function.
 */
export function getResearchTopic(messages: BaseMessage[]): string {
  // Check if request has a history and combine the messages into a single string
  if (messages.length === 1) {
    return messages[messages.length - 1].content as string;
  } else {
    let researchTopic = '';
    for (const message of messages) {
      if (message._getType() === 'human') {
        researchTopic += `User: ${message.content}\n`;
      } else if (message._getType() === 'ai') {
        researchTopic += `Assistant: ${message.content}\n`;
      }
    }
    return researchTopic;
  }
}

/**
 * Create a map of the vertex ai search urls (very long) to a short url with a unique id for each url.
 * Ensures each original URL gets a consistent shortened form while maintaining uniqueness.
 * Equivalent to the Python resolve_urls function.
 * Updated for the new @google/genai SDK response format.
 */
export function resolveUrls(urlsToResolve: any[], id: number): Record<string, string> {
  const prefix = 'https://vertexaisearch.cloud.google.com/id/';

  if (!urlsToResolve || urlsToResolve.length === 0) {
    return {};
  }

  // Extract URLs from grounding chunks, handling both old and new formats
  const urls = urlsToResolve.map(site => {
    if (site.web && site.web.uri) {
      return site.web.uri;
    }
    // Handle potential new format
    if (site.uri) {
      return site.uri;
    }
    return null;
  }).filter(url => url !== null);

  // Create a dictionary that maps each unique URL to its first occurrence index
  const resolvedMap: Record<string, string> = {};
  urls.forEach((url, idx) => {
    if (url && !(url in resolvedMap)) {
      resolvedMap[url] = `${prefix}${id}-${idx}`;
    }
  });

  return resolvedMap;
}

/**
 * Inserts citation markers into a text string based on start and end indices.
 * Equivalent to the Python insert_citation_markers function.
 */
export function insertCitationMarkers(text: string, citationsList: Citation[]): string {
  // Sort citations by end_index in descending order.
  // If end_index is the same, secondary sort by start_index descending.
  // This ensures that insertions at the end of the string don't affect
  // the indices of earlier parts of the string that still need to be processed.
  const sortedCitations = [...citationsList].sort((a, b) => {
    if (a.end_index === b.end_index) {
      return b.start_index - a.start_index;
    }
    return b.end_index - a.end_index;
  });

  let modifiedText = text;
  for (const citationInfo of sortedCitations) {
    // These indices refer to positions in the *original* text,
    // but since we iterate from the end, they remain valid for insertion
    // relative to the parts of the string already processed.
    const endIdx = citationInfo.end_index;
    let markerToInsert = '';
    for (const segment of citationInfo.segments) {
      markerToInsert += ` [${segment.label}](${segment.short_url})`;
    }
    // Insert the citation marker at the original end_idx position
    modifiedText = modifiedText.slice(0, endIdx) + markerToInsert + modifiedText.slice(endIdx);
  }

  return modifiedText;
}

/**
 * Create a map of the vertex ai search urls (very long) to a short url with a unique id for each url.
 * Equivalent to the Python resolve_urls function.
 */
export function resolveUrls(urlsToResolve: any[], id: number): Record<string, string> {
  const prefix = 'https://vertexaisearch.cloud.google.com/id/';
  const urls = urlsToResolve.map(site => site.web?.uri).filter(Boolean);

  // Create a dictionary that maps each unique URL to its first occurrence index
  const resolvedMap: Record<string, string> = {};
  for (let idx = 0; idx < urls.length; idx++) {
    const url = urls[idx];
    if (url && !resolvedMap[url]) {
      resolvedMap[url] = `${prefix}${id}-${idx}`;
    }
  }

  return resolvedMap;
}

/**
 * Extracts and formats citation information from a Gemini model's response.
 * Equivalent to the Python get_citations function.
 * Updated for the new @google/genai SDK response format.
 */
export function getCitations(response: any, resolvedUrlsMap: Record<string, string>): Citation[] {
  const citations: Citation[] = [];

  // Ensure response and necessary nested structures are present
  if (!response || !response.candidates) {
    return citations;
  }

  const candidate = response.candidates[0];

  // Handle both old and new response formats
  const groundingMetadata = candidate.groundingMetadata || candidate.grounding_metadata;
  if (!groundingMetadata || !groundingMetadata.groundingSupports) {
    return citations;
  }

  for (const support of groundingMetadata.groundingSupports) {
    const citation: Citation = {
      start_index: 0,
      end_index: 0,
      segments: [],
    };

    // Ensure segment information is present
    if (!support.segment) {
      continue; // Skip this support if segment info is missing
    }

    const startIndex = support.segment.startIndex !== null ? support.segment.startIndex : 0;

    // Ensure end_index is present to form a valid segment
    if (support.segment.endIndex === null) {
      continue; // Skip if end_index is missing, as it's crucial
    }

    citation.start_index = startIndex;
    citation.end_index = support.segment.endIndex;

    citation.segments = [];
    if (support.groundingChunkIndices) {
      for (const ind of support.groundingChunkIndices) {
        try {
          const chunk = groundingMetadata.groundingChunks[ind];
          const resolvedUrl = resolvedUrlsMap[chunk.web.uri];
          if (resolvedUrl) {
            citation.segments.push({
              label: chunk.web.title?.split('.')[0] || `Source ${ind + 1}`,
              short_url: resolvedUrl,
              value: chunk.web.uri,
            });
          }
        } catch (error) {
          // Handle cases where chunk, web, uri, or resolved_map might be problematic
          // For simplicity, we'll just skip adding this particular segment link
          console.warn('Error processing citation chunk:', error);
          continue;
        }
      }
    }

    if (citation.segments.length > 0) {
      citations.push(citation);
    }
  }
  return citations;
}
