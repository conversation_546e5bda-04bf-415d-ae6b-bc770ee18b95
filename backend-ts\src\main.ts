import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/exception.filter';
import { LoggingInterceptor } from './common/logging.interceptor';
import { Logger } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // Create NestJS application with Fastify adapter
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      logger: process.env.NODE_ENV === 'production',
      bodyLimit: 10 * 1024 * 1024, // 10MB body limit
    }),
  );

  // Apply global exception filter for consistent error handling
  app.useGlobalFilters(new GlobalExceptionFilter());

  // Apply global logging interceptor for monitoring
  app.useGlobalInterceptors(new LoggingInterceptor());

  // Enable CORS (matching Python FastAPI configuration)
  app.enableCors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: '*',
    credentials: true,
  });

  // Check if frontend build directory exists
  const frontendPath = path.join(__dirname, '..', '..', 'frontend', 'dist');
  if (fs.existsSync(frontendPath) && fs.existsSync(path.join(frontendPath, 'index.html'))) {
    // Serve static frontend files
    app.useStaticAssets({
      root: frontendPath,
      prefix: '/app',
    });
  } else {
    console.warn(`WARN: Frontend build directory not found or incomplete at ${frontendPath}. Serving frontend will likely fail.`);
  }

  // Use environment-based port configuration to match Python backend
  // DEV: 2024, PROD: 8123 (matching frontend expectations)
  const isDev = process.env.NODE_ENV !== 'production';
  const port = process.env.PORT ? parseInt(process.env.PORT, 10) : (isDev ? 2024 : 8123);

  // Graceful shutdown handling
  process.on('SIGTERM', async () => {
    logger.log('SIGTERM received, shutting down gracefully');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('SIGINT received, shutting down gracefully');
    await app.close();
    process.exit(0);
  });

  // Start the server
  await app.listen(port, '0.0.0.0');
  logger.log(`🚀 Application is running on: http://localhost:${port}`);
  logger.log(`📊 Environment: ${isDev ? 'development' : 'production'}`);
  logger.log(`🏥 Health checks available at: http://localhost:${port}/health`);
  logger.log(`🔍 Detailed health: http://localhost:${port}/health/detailed`);
}

bootstrap();
